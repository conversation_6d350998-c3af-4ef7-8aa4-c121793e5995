"use client"
import { useState } from 'react';

const DebugPage = () => {
  const [testResults, setTestResults] = useState<any[]>([]);

  const runTests = async () => {
    const results = [];
    
    // Test 1: Check if API router is working
    try {
      const response = await fetch('/api/test');
      const data = await response.json();
      results.push({
        test: 'API Router Test',
        status: response.status,
        success: response.ok,
        data
      });
    } catch (error) {
      results.push({
        test: 'API Router Test',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Check auth session endpoint
    try {
      const response = await fetch('/api/auth/session', {
        credentials: 'include'
      });
      const data = response.ok ? await response.json() : await response.text();
      results.push({
        test: 'Auth Session Test',
        status: response.status,
        success: response.ok,
        data
      });
    } catch (error) {
      results.push({
        test: 'Auth Session Test',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Check Google auth endpoint
    try {
      const response = await fetch('/api/auth/sign-in/google', {
        method: 'GET',
        credentials: 'include'
      });
      results.push({
        test: 'Google Auth Endpoint Test',
        status: response.status,
        success: response.status === 302 || response.ok,
        redirected: response.redirected,
        url: response.url
      });
    } catch (error) {
      results.push({
        test: 'Google Auth Endpoint Test',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setTestResults(results);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Authentication Debug Page</h1>
        
        <button
          onClick={runTests}
          className="mb-6 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Run Tests
        </button>

        <div className="space-y-4">
          {testResults.map((result, index) => (
            <div key={index} className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-2">{result.test}</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Success:</strong> 
                  <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                    {result.success ? ' ✓' : ' ✗'}
                  </span>
                </div>
                {result.status && (
                  <div><strong>Status:</strong> {result.status}</div>
                )}
                {result.error && (
                  <div className="col-span-2">
                    <strong>Error:</strong> 
                    <span className="text-red-600">{result.error}</span>
                  </div>
                )}
                {result.data && (
                  <div className="col-span-2">
                    <strong>Data:</strong>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
                {result.redirected && (
                  <div className="col-span-2">
                    <strong>Redirected to:</strong> {result.url}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Environment Info</h3>
          <div className="text-sm space-y-1">
            <div><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
            <div><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent : 'N/A'}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPage;
