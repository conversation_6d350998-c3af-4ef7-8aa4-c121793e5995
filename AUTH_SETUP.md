# Authentication Setup Guide

This guide explains how the Better Auth integration works with the Blade framework and RONIN database.

## Overview

The authentication system uses:
- **Better Auth** for authentication logic
- **RONIN** database for data storage
- **Hono** for API routing
- **Blade** framework for the frontend

## File Structure

```
lib/
├── auth.ts          # Better Auth server configuration
├── auth-client.ts   # Better Auth client configuration
└── auth-server.ts   # Server-side session helpers for Blade

components/auth/
├── loginform.tsx    # Login form component
├── registerform.tsx # Registration form component
└── logoutbutton.tsx # Logout button component

pages/
├── login.tsx        # Login page
├── register.tsx     # Registration page
└── dashboard.tsx    # Protected dashboard page

router.ts            # API routes (includes auth endpoints)
schema/index.ts      # Database schema for auth tables
```

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env` and fill in your values:

```bash
cp .env.example .env
```

Required variables:
- `RONIN_TOKEN`: Your RONIN database token

Optional variables for social auth:
- `GITHUB_CLIENT_ID` and `GITHUB_CLIENT_SECRET`
- `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`

### 2. Database Schema

The required database models are defined in `schema/index.ts`:
- `User`: User accounts
- `Session`: User sessions
- `Account`: OAuth accounts
- `Verification`: Email verification tokens

### 3. API Routes

Authentication endpoints are automatically available at:
- `POST /api/auth/sign-in/email` - Email/password sign in
- `POST /api/auth/sign-up/email` - Email/password sign up
- `GET /api/auth/sign-in/github` - GitHub OAuth
- `GET /api/auth/sign-in/google` - Google OAuth
- `POST /api/auth/sign-out` - Sign out
- `GET /api/user` - Get current user info

## Usage

### Client-Side Authentication

```tsx
import authClient from '../lib/auth-client';

// Sign in with email/password
const { data, error } = await authClient.signIn.email({
  email: '<EMAIL>',
  password: 'password123'
});

// Sign in with social provider
await authClient.signIn.social({
  provider: 'github',
  callbackURL: '/dashboard'
});

// Sign out
await authClient.signOut();
```

### Server-Side Authentication

```tsx
import { requireAuth, getUser } from '../lib/auth-server';

// Require authentication (throws error if not authenticated)
const session = await requireAuth();

// Optional authentication (returns null if not authenticated)
const user = await getUser();
```

## Components

### Login Form
- Email/password authentication
- Social login buttons (GitHub, Google)
- Error handling and loading states

### Register Form
- Email/password registration
- Form validation
- Success/error messages

### Logout Button
- Simple logout functionality
- Loading state during logout

## Troubleshooting

### Common Issues

1. **"Authentication required" error**: Make sure you have a valid session cookie
2. **Social auth not working**: Check your OAuth app configuration and environment variables
3. **Database errors**: Verify your RONIN_TOKEN is correct and the schema is deployed

### Development Tips

- Use the `/api/user` endpoint to check authentication status
- Check browser cookies for `better-auth.session_token`
- Monitor the console for authentication errors
