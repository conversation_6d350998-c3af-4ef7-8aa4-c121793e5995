import { useState, useEffect } from 'react';
import { authClient } from '../lib/auth-client';
import type { Session } from '../lib/auth';

export function useAuth() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const { data } = await authClient.getSession();
        setSession(data);
      } catch (error) {
        console.error('Failed to fetch session:', error);
        setSession(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();
  }, []);

  const login = async (provider: 'github' | 'google') => {
    try {
      const result = await authClient.signIn.social({
        provider,
        callbackURL: '/'
      });

      if (result.data && 'user' in result.data) {
        // For social login, we need to refetch the session after redirect
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            setSession(null);
            window.location.href = '/';
          }
        }
      });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const signup = async (email: string, password: string, name: string) => {
    try {
      await authClient.signUp.email({
        email,
        password,
        name,
        callbackURL: '/dashboard'
      });
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    }
  };

  return {
    session,
    isLoading,
    isAuthenticated: !!session,
    login,
    logout,
    signup
  };
}
