import { useState, useEffect } from 'react';
import { authClient } from '../lib/auth-client';
import type { Session } from '../lib/auth';

export function useAuth() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setError(null);
        const { data } = await authClient.getSession();
        setSession(data);
      } catch (error) {
        console.error('Failed to fetch session:', error);
        setSession(null);
        setError('Failed to fetch session');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();
  }, []);

  const login = async (provider: 'github' | 'google') => {
    try {
      setError(null);
      setIsLoading(true);

      // For social login, Better Auth will redirect to the provider
      // and then back to our callback URL
      const result = await authClient.signIn.social({
        provider,
        callbackURL: window.location.origin + '/'
      });

      // If we get here without redirect, something went wrong
      if (result.error) {
        throw new Error(result.error.message || 'Login failed');
      }

    } catch (error) {
      console.error('Login failed:', error);
      setError(error instanceof Error ? error.message : 'Login failed');
      setIsLoading(false);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            setSession(null);
            window.location.href = '/';
          }
        }
      });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const signup = async (email: string, password: string, name: string) => {
    try {
      await authClient.signUp.email({
        email,
        password,
        name,
        callbackURL: '/dashboard'
      });
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    }
  };

  return {
    session,
    isLoading,
    isAuthenticated: !!session,
    error,
    login,
    logout,
    signup
  };
}
