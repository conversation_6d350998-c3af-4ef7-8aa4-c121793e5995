import { betterAuth } from 'better-auth';

// Test with the simplest possible Better Auth setup - no database
console.log('Creating simple Better Auth instance (no database)...');

export const authSimple = betterAuth({
  secret: process.env["BETTER_AUTH_SECRET"] as string,
  baseURL: process.env["BETTER_AUTH_URL"] || "http://localhost:3000",
  basePath: "/api/auth",
  socialProviders: {
    github: {
      clientId: process.env["GITHUB_CLIENT_ID"] as string,
      clientSecret: process.env["GITHUB_CLIENT_SECRET"] as string,
    },
    google: {
      clientId: process.env["GOOGLE_CLIENT_ID"] as string,
      clientSecret: process.env["GOOGLE_CLIENT_SECRET"] as string,
    },
  },
});

console.log('Simple Better Auth created successfully');
