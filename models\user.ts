// models/user.ts
export interface User {
  id: string;
  email: string;
  name: string;
  handle?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  emailVerified?: boolean;
}

export interface UserProfile extends User {
  bio?: string;
  website?: string;
  location?: string;
  preferences?: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

export interface CreateUserData {
  email: string;
  name: string;
  password: string;
}

export interface UpdateUserData {
  name?: string;
  bio?: string;
  website?: string;
  location?: string;
  preferences?: Partial<UserProfile['preferences']>;
}
