// Test simple Better Auth setup
import { authSimple } from './lib/auth-simple.ts';

console.log('Testing simple Better Auth...');

// Test the routes that should work
const testRoutes = [
  '/api/auth/session',
  '/api/auth/sign-in/google',
  '/api/auth/callback/google'
];

for (const route of testRoutes) {
  try {
    const testRequest = new Request(`http://localhost:3000${route}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log(`Testing ${route}...`);
    const response = await authSimple.handler(testRequest);
    console.log(`  Status: ${response.status} (${response.ok ? 'OK' : 'Error'})`);
    
    if (response.status !== 404) {
      console.log(`  SUCCESS! Route ${route} is working!`);
      if (response.status < 400) {
        try {
          const text = await response.text();
          console.log(`  Response: ${text.substring(0, 100)}...`);
        } catch (e) {
          console.log(`  Response: [Could not read body]`);
        }
      }
    }
  } catch (error) {
    console.error(`  Error testing ${route}:`, error.message);
  }
}

console.log('Simple auth test complete.');
