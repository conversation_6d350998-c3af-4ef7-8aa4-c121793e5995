"use client"
import { Link } from '@ronin/blade/client/components';

const IndexPage = () => {
  // Simple landing page to test basic functionality
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Welcome to Your Blade App
          </h1>
          <p className="mt-4 text-xl text-gray-600">
            This is a simple landing page to test basic functionality.
          </p>

          <div className="mt-8 space-x-4">
            <Link href="/login">
              <a className="inline-block px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Go to Login
              </a>
            </Link>

            <Link href="/debug">
              <a className="inline-block px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700">
                Debug Page
              </a>
            </Link>
          </div>

          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Testing Instructions</h3>
            <div className="text-sm text-left space-y-2">
              <p>1. Click "Go to Login" to test the login page</p>
              <p>2. Click "Debug Page" to test API endpoints</p>
              <p>3. Check browser console for any errors</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndexPage;