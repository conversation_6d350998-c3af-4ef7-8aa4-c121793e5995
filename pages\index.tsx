"use client"
import { useAuth } from '../hooks/use-auth';
import { useRedirect } from '@ronin/blade/hooks';
import { useEffect, useState } from 'react';

const IndexPage = () => {
  const { isAuthenticated, session, isLoading, error } = useAuth();
  const redirect = useRedirect();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    console.log('Auth state:', { isLoading, isAuthenticated, error, session });

    // Wait a bit longer before redirecting to ensure auth check is complete
    const timer = setTimeout(() => {
      if (!isLoading && !isAuthenticated && !hasRedirected) {
        console.log('Redirecting to login...');
        setHasRedirected(true);
        redirect('/login');
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, redirect, hasRedirected, error, session]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading authentication...</div>
        <div className="text-sm text-gray-500 mt-2">
          Checking session status...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">Authentication error: {error}</div>
          <button
            onClick={() => redirect('/login')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg mb-4">Not authenticated</div>
          <button
            onClick={() => redirect('/login')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Welcome, {session?.user?.name}!
            </h1>
            <p className="text-gray-600 mb-4">
              You are successfully logged in.
            </p>
            {session?.user?.image && (
              <img
                src={session.user.image}
                alt="Profile"
                className="w-16 h-16 rounded-full"
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndexPage;