import { Link } from '@ronin/blade/client/components';

const IndexPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Welcome to Your Blade App
          </h1>

          {/* Authentication Status Section */}
          <div className="mt-8">
            <div className="bg-white rounded-lg shadow p-6 max-w-md mx-auto">
              <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>

              <div className="space-y-4">
                <p className="text-gray-600">
                  Check your authentication status and manage your session:
                </p>

                <div className="space-y-2">
                  <Link href="/api/auth/session">
                    <a className="block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                      Check Session (JSON)
                    </a>
                  </Link>

                  <Link href="/auth">
                    <a className="block px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                      Sign In / Sign Up
                    </a>
                  </Link>

                  <Link href="/api/auth/sign-out">
                    <a className="block px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                      Sign Out
                    </a>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Testing Instructions</h3>
            <div className="text-sm text-left space-y-2">
              <p>1. Click "Sign In / Sign Up" to authenticate with Google/GitHub</p>
              <p>2. After signing in, click "Check Session" to see your user data</p>
              <p>3. You should see your name, email, and profile image in the JSON</p>
              <p>4. Click "Sign Out" to test the logout functionality</p>
              <p>5. Refresh the page to see the updated authentication state</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndexPage;