"use-client"
import { useAuth } from '../hooks/use-auth';
import { useRedirect } from '@ronin/blade/hooks';
import { useEffect } from 'react';

const IndexPage = () => {
  const { isAuthenticated, session } = useAuth();
  const redirect = useRedirect();
  useEffect(() => {
    if (!isAuthenticated) {
      redirect('/login');
    }
  }, [isAuthenticated, redirect]);

  

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Welcome, {session?.user?.name}!
            </h1>
            <p className="text-gray-600 mb-4">
              You are successfully logged in.
            </p>
            <img 
              src={session?.user?.image} 
              alt="Profile" 
              className="w-16 h-16 rounded-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndexPage;