import { useMetadata } from '@ronin/blade/server/hooks';

const DebugSimplePage = () => {
  useMetadata({
    title: 'Simple Debug Page'
  });

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Simple Debug Page (Server-Side)</h1>
        
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Test Links (These should work)</h2>
            <div className="space-y-2">
              <div>
                <a 
                  href="/api/test" 
                  className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  target="_blank"
                >
                  Test API Endpoint
                </a>
              </div>
              
              <div>
                <a 
                  href="/api/auth-debug" 
                  className="inline-block px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  target="_blank"
                >
                  Test Auth Debug
                </a>
              </div>
              
              <div>
                <a 
                  href="/api/auth/session" 
                  className="inline-block px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
                  target="_blank"
                >
                  Test Auth Session
                </a>
              </div>
              
              <div>
                <a 
                  href="/api/auth/sign-in/google" 
                  className="inline-block px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  Test Google OAuth
                </a>
              </div>
              
              <div>
                <a 
                  href="/api/auth/sign-in/github" 
                  className="inline-block px-4 py-2 bg-gray-800 text-white rounded hover:bg-gray-900"
                >
                  Test GitHub OAuth
                </a>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Environment Check</h2>
            <div className="text-sm space-y-2">
              <div><strong>Node Environment:</strong> {process.env.NODE_ENV || 'development'}</div>
              <div><strong>Has RONIN Token:</strong> {process.env.RONIN_TOKEN ? '✓ Yes' : '✗ No'}</div>
              <div><strong>Has Auth Secret:</strong> {process.env.BETTER_AUTH_SECRET ? '✓ Yes' : '✗ No'}</div>
              <div><strong>Has Google Client ID:</strong> {process.env.GOOGLE_CLIENT_ID ? '✓ Yes' : '✗ No'}</div>
              <div><strong>Has GitHub Client ID:</strong> {process.env.GITHUB_CLIENT_ID ? '✓ Yes' : '✗ No'}</div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Instructions</h2>
            <div className="text-sm space-y-2">
              <p>1. Click each link above to test different endpoints</p>
              <p>2. The API endpoints should open in new tabs and show JSON responses</p>
              <p>3. The OAuth links should redirect to Google/GitHub or show an error</p>
              <p>4. Check the server console for detailed logs</p>
              <p>5. If OAuth shows 404, there's an issue with Better Auth setup</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugSimplePage;
