import { Hono } from "hono";
import { auth } from "./lib/auth";
import { cors } from "hono/cors";
import { ronin } from "@ronin/hono";

const app = new Hono();

// Add RONIN middleware with explicit token
app.use("*", ronin({
  token: process.env["RONIN_TOKEN"] as string
}));

// Configure CORS for all routes (Better Auth needs this)
app.use(
  "*",
  cors({
    origin: process.env["BETTER_AUTH_URL"] || "http://localhost:3000",
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Test endpoint to verify router is working
app.get("/api/test", (c) => {
  return c.json({ message: "Router is working", timestamp: new Date().toISOString() });
});

// Test what Better Auth expects
app.get("/api/auth-routes", async (c) => {
  try {
    // Test different paths to see what Better Auth responds to
    const testPaths = [
      '/api/auth/session',
      '/api/auth/sign-in/google',
      '/api/auth/sign-in/github',
      '/api/auth/callback/google',
      '/api/auth/callback/github'
    ];

    const results = [];

    for (const path of testPaths) {
      try {
        const testRequest = new Request(`http://localhost:3000${path}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        const response = await auth.handler(testRequest);
        results.push({
          path,
          status: response.status,
          ok: response.ok
        });
      } catch (error) {
        results.push({
          path,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return c.json({
      message: "Better Auth route test results",
      results
    });
  } catch (error) {
    return c.json({
      error: 'Route test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Debug endpoint to show available auth routes
app.get("/api/auth-debug", async (c) => {
  try {
    // Test Better Auth directly
    let authTestResult = 'Unknown';
    let authApiTest = 'Unknown';

    try {
      // Test if auth.api exists and works
      if (auth.api && auth.api.getSession) {
        authApiTest = 'auth.api.getSession exists';
        // Try to get session without headers (should return null, not error)
        const sessionResult = await auth.api.getSession({ headers: new Headers() });
        authApiTest = `auth.api works: ${sessionResult ? 'has session' : 'no session'}`;
      } else {
        authApiTest = 'auth.api.getSession not found';
      }
    } catch (apiError) {
      authApiTest = `auth.api error: ${apiError instanceof Error ? apiError.message : 'Unknown'}`;
    }

    try {
      // Test the handler directly
      const testRequest = new Request('http://localhost:3000/api/auth/session', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      const authResponse = await auth.handler(testRequest);
      authTestResult = `Handler Status: ${authResponse.status}`;
    } catch (authError) {
      authTestResult = `Handler Error: ${authError instanceof Error ? authError.message : 'Unknown error'}`;
    }

    return c.json({
      message: "Auth debug info",
      authTest: authTestResult,
      authApiTest: authApiTest,
      betterAuthVersion: 'unknown', // We can't easily get this
      availableRoutes: [
        "/api/auth/session",
        "/api/auth/sign-in/google",
        "/api/auth/sign-in/github",
        "/api/auth/sign-up/email",
        "/api/auth/sign-in/email",
        "/api/auth/callback/google",
        "/api/auth/callback/github"
      ],
      env: {
        hasGoogleId: !!process.env["GOOGLE_CLIENT_ID"],
        hasGoogleSecret: !!process.env["GOOGLE_CLIENT_SECRET"],
        hasGithubId: !!process.env["GITHUB_CLIENT_ID"],
        hasGithubSecret: !!process.env["GITHUB_CLIENT_SECRET"],
        hasAuthSecret: !!process.env["BETTER_AUTH_SECRET"]
      }
    });
  } catch (error) {
    return c.json({
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Test endpoint to see if specific routes work
app.get("/api/auth/test-route", (c) => {
  return c.json({ message: "Auth route test works!" });
});

// Mount Better Auth handler - exact pattern from Better Auth docs
app.on(["POST", "GET"], "/api/auth/*", async (c) => {
  console.log('Auth endpoint hit:', c.req.method, c.req.url);
  console.log('Auth path:', new URL(c.req.url).pathname);

  // Test if this is our test route
  if (c.req.url.includes('/test-route')) {
    console.log('Test route detected, skipping Better Auth');
    return c.json({ message: "Test route works!" });
  }

  try {
    console.log('Calling Better Auth handler...');
    const response = await auth.handler(c.req.raw);
    console.log('Better Auth response status:', response.status);

    if (response.status === 404) {
      console.log('Better Auth returned 404 - route not recognized');
    } else {
      console.log('Better Auth handled the route successfully');
    }

    return response;
  } catch (error) {
    console.error('Better Auth handler error:', error);
    return c.json({
      error: 'Auth handler failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

export default app;
