import { Hono } from "hono";
import { auth } from "./lib/auth";
import { cors } from "hono/cors";

const app = new Hono();

// Configure CORS for auth endpoints
app.use(
  "/api/auth/*",
  cors({
    origin: process.env.PUBLIC_APP_URL || "http://localhost:3000",
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Test endpoint to verify router is working
app.get("/api/test", (c) => {
  return c.json({ message: "Router is working", timestamp: new Date().toISOString() });
});

// Debug endpoint to show available auth routes
app.get("/api/auth-debug", async (c) => {
  try {
    // Test if Better Auth is working by trying to get session
    const testRequest = new Request('http://localhost:3000/api/auth/session', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });

    let authTestResult = 'Unknown';
    try {
      const authResponse = await auth.handler(testRequest);
      authTestResult = `Status: ${authResponse.status}`;
    } catch (authError) {
      authTestResult = `Error: ${authError instanceof Error ? authError.message : 'Unknown error'}`;
    }

    return c.json({
      message: "Auth debug info",
      authTest: authTestResult,
      availableRoutes: [
        "/api/auth/session",
        "/api/auth/sign-in/google",
        "/api/auth/sign-in/github",
        "/api/auth/sign-up/email",
        "/api/auth/sign-in/email",
        "/api/auth/callback/google",
        "/api/auth/callback/github"
      ],
      env: {
        hasGoogleId: !!process.env["GOOGLE_CLIENT_ID"],
        hasGoogleSecret: !!process.env["GOOGLE_CLIENT_SECRET"],
        hasGithubId: !!process.env["GITHUB_CLIENT_ID"],
        hasGithubSecret: !!process.env["GITHUB_CLIENT_SECRET"],
        hasAuthSecret: !!process.env["BETTER_AUTH_SECRET"]
      }
    });
  } catch (error) {
    return c.json({
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Add specific routes for debugging
app.get("/api/auth/session", async (c) => {
  console.log('Session endpoint hit');
  try {
    const response = await auth.handler(c.req.raw);
    console.log('Session response status:', response.status);
    return response;
  } catch (error) {
    console.error('Session handler error:', error);
    return c.json({ error: 'Session error' }, 500);
  }
});

// Mount auth handler - Better Auth expects the full request object
app.all("/api/auth/*", async (c) => {
  console.log('Auth endpoint hit:', c.req.method, c.req.url);
  console.log('Auth path:', new URL(c.req.url).pathname);
  try {
    const response = await auth.handler(c.req.raw);
    console.log('Auth response status:', response.status);
    return response;
  } catch (error) {
    console.error('Auth handler error:', error);
    return c.json({
      error: 'Authentication error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

export default app;
