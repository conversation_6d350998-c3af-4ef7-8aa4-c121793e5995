import { Hono } from "hono";
import { auth } from "./lib/auth";
import { cors } from "hono/cors";

const app = new Hono();

// Configure CORS for auth endpoints
app.use(
  "/api/auth/*",
  cors({
    origin: process.env.PUBLIC_APP_URL || "http://localhost:3000",
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Test endpoint to verify router is working
app.get("/api/test", (c) => {
  return c.json({ message: "Router is working", timestamp: new Date().toISOString() });
});

// Mount auth handler - Better Auth expects the full request object
app.on(["POST", "GET"], "/api/auth/*", async (c) => {
  console.log('Auth endpoint hit:', c.req.method, c.req.url);
  try {
    const response = await auth.handler(c.req.raw);
    console.log('Auth response status:', response.status);
    return response;
  } catch (error) {
    console.error('Auth handler error:', error);
    return c.json({ error: 'Authentication error' }, 500);
  }
});

export default app;
