import { auth } from './auth';

export async function getUser(request?: Request) {
  try {
    const headers = request?.headers || new Headers();

    const session = await auth.api.getSession({
      headers: headers
    });

    return session;
  } catch (error) {
    console.error('Failed to get user session:', error);
    return null;
  }
}

export async function requireAuth(request?: Request) {
  const session = await getUser(request);

  if (!session) {
    throw new Error('Authentication required');
  }

  return session;
}
