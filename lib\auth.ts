import { betterAuth } from 'better-auth';
import { ronin } from '@ronin/better-auth';

console.log('Initializing Better Auth with config:', {
  hasSecret: !!process.env["BETTER_AUTH_SECRET"],
  baseURL: process.env["BETTER_AUTH_URL"] || "http://localhost:3000",
  hasGoogleId: !!process.env["GOOGLE_CLIENT_ID"],
  hasGithubId: !!process.env["GITHUB_CLIENT_ID"],
  hasRoninToken: !!process.env["RONIN_TOKEN"],
});

export const auth = betterAuth({
  database: ronin(), // Use default RONIN adapter
  secret: process.env["BETTER_AUTH_SECRET"] as string,
  baseURL: process.env["BETTER_AUTH_URL"] || "http://localhost:3000",
  basePath: "/api/auth", // Explicitly set the base path
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env["GITHUB_CLIENT_ID"] as string,
      clientSecret: process.env["GITHUB_CLIENT_SECRET"] as string,
    },
    google: {
      clientId: process.env["GOOGLE_CLIENT_ID"] as string,
      clientSecret: process.env["GOOGLE_CLIENT_SECRET"] as string,
    },
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: true
    },
    defaultCookieAttributes: {
      sameSite: "lax",
      secure: process.env["NODE_ENV"] === "production",
      path: "/"
    }
  }
});

export type Session = typeof auth.$Infer.Session;