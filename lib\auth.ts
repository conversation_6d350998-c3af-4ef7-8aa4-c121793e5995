import { betterAuth } from 'better-auth';
import { ronin } from '@ronin/better-auth';

export const auth = betterAuth({
  database: ronin(),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env["GITHUB_CLIENT_ID"] as string,
      clientSecret: process.env["GITHUB_CLIENT_SECRET"] as string,
      scopes: ["user:email"]
    },
    google: {
      clientId: process.env["GOOGLE_CLIENT_ID"] as string,
      clientSecret: process.env["GOOGLE_CLIENT_SECRET"] as string,
      scopes: ["email", "profile"]
    },
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: true
    },
    defaultCookieAttributes: {
      sameSite: "lax",
      secure: process.env["NODE_ENV"] === "production",
      path: "/"
    }
  }
});

export type Session = typeof auth.$Infer.Session;