import { betterAuth } from "better-auth"
import { ronin as ronin<PERSON>dapter } from "@ronin/better-auth"
import ronin from 'ronin';

console.log('Initializing Better Auth with RONIN database...');

const client = ronin({
  token: process.env["RONIN_TOKEN"],
})

export const auth = betterAuth({
  database: ronin<PERSON><PERSON>pter(client),
  secret: process.env["BETTER_AUTH_SECRET"] as string,
  baseURL: "http://localhost:3000",
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env["GITHUB_CLIENT_ID"] as string,
      clientSecret: process.env["GITHUB_CLIENT_SECRET"] as string,
    },
    google: {
      clientId: process.env["GOOGLE_CLIENT_ID"] as string,
      clientSecret: process.env["GOOGLE_CLIENT_SECRET"] as string,
    },
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: true
    },
    defaultCookieAttributes: {
      sameSite: "lax",
      secure: process.env["NODE_ENV"] === "production",
      path: "/"
    }
  }
});

console.log('Better Auth instance created with RONIN');

export type AuthType = {
  Variables: {
    user: typeof auth.$Infer.Session.user | null
    session: typeof auth.$Infer.Session.session | null
  }
}