import { betterAuth } from 'better-auth';
import Database from 'better-sqlite3';

console.log('Initializing Better Auth with SQLite database...');

export const auth = betterAuth({
  database: new Database("./auth.sqlite"), // Use direct SQLite
  secret: process.env["BETTER_AUTH_SECRET"] as string,
  baseURL: "http://localhost:3000", // Remove environment variable for testing
  // Remove basePath to let Better Auth use defaults
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env["GITHUB_CLIENT_ID"] as string,
      clientSecret: process.env["GITHUB_CLIENT_SECRET"] as string,
    },
    google: {
      clientId: process.env["GOOGLE_CLIENT_ID"] as string,
      clientSecret: process.env["GOOGLE_CLIENT_SECRET"] as string,
    },
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: true
    },
    defaultCookieAttributes: {
      sameSite: "lax",
      secure: process.env["NODE_ENV"] === "production",
      path: "/"
    }
  }
});

console.log('Better Auth instance created');
console.log('Auth object keys:', Object.keys(auth));
if (auth.options) {
  console.log('Auth options baseURL:', auth.options.baseURL);
  console.log('Auth options keys:', Object.keys(auth.options));
}

export type Session = typeof auth.$Infer.Session;