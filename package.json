{"name": "basic-example", "version": "1.0.0", "private": true, "scripts": {"dev": "blade", "build": "blade build", "serve": "blade serve"}, "author": "ronin", "dependencies": {"@ronin/better-auth": "^1.0.0", "@ronin/blade": "0.8.10", "@ronin/hono": "^0.1.2", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "hono": "^4.8.1", "react": "0.0.0-experimental-df12d7eac-20230510", "react-dom": "0.0.0-experimental-df12d7eac-20230510", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "19.1.4", "@types/react-dom": "19.1.5"}}